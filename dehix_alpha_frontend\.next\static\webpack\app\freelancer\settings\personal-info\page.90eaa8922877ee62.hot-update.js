"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/personal-info/page",{

/***/ "(app-pages-browser)/./src/components/fileUpload/resume.tsx":
/*!**********************************************!*\
  !*** ./src/components/fileUpload/resume.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,Trash2,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,Trash2,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,Trash2,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,Trash2,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,FileText,Trash2,UploadCloud,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud-upload.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst allowedResumeFormats = [\n    \"application/pdf\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n];\nconst maxResumeSize = 5 * 1024 * 1024; // 5MB\nconst ResumeUpload = (param)=>{\n    let { onResumeUpdate, refreshTrigger, userId } = param;\n    _s();\n    const [selectedResume, setSelectedResume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedFileName, setUploadedFileName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumePreviewURL, setResumePreviewURL] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingResumeUrl, setExistingResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRemoving, setIsRemoving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const truncateFileName = (fileName)=>{\n        if (!fileName) return \"\"; // Handle undefined values\n        const maxLength = 20;\n        const extension = fileName.includes(\".\") ? fileName.substring(fileName.lastIndexOf(\".\")) : \"\"; // Handle files without extension\n        return fileName.length > maxLength ? \"\".concat(fileName.substring(0, maxLength - extension.length), \"...\").concat(extension) : fileName;\n    };\n    const extractFileNameFromUrl = (url)=>{\n        try {\n            const urlParts = url.split(\"/\");\n            const fileName = urlParts[urlParts.length - 1];\n            // Remove query parameters if any\n            return fileName.split(\"?\")[0] || \"resume.pdf\";\n        } catch (e) {\n            return \"resume.pdf\";\n        }\n    };\n    const handleRemoveResume = async ()=>{\n        try {\n            setIsRemoving(true);\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.put(\"/freelancer\", {\n                resume: null\n            });\n            setExistingResumeUrl(null);\n            setUploadedFileName(null);\n            setResumePreviewURL(null);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Success\",\n                description: \"Resume removed successfully!\"\n            });\n        } catch (error) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to remove resume. Please try again.\"\n            });\n        } finally{\n            setIsRemoving(false);\n        }\n    };\n    const handleResumeChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            if (allowedResumeFormats.includes(file.type)) {\n                if (file.size <= maxResumeSize) {\n                    setSelectedResume(file);\n                    setUploadedFileName(file.name);\n                    // Create a preview URL only for PDFs\n                    if (file.type === \"application/pdf\") {\n                        const fileURL = URL.createObjectURL(file);\n                        setResumePreviewURL(fileURL);\n                    } else {\n                        setResumePreviewURL(null);\n                    }\n                } else {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                        variant: \"destructive\",\n                        title: \"File too large\",\n                        description: \"Resume size should not exceed 5MB.\"\n                    });\n                }\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    variant: \"destructive\",\n                    title: \"Invalid file type\",\n                    description: \"Supported formats: PDF, DOCX.\"\n                });\n            }\n        }\n    };\n    const handleUploadClick = async (e)=>{\n        e.preventDefault();\n        if (!selectedResume) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                variant: \"destructive\",\n                title: \"No Resume Selected\",\n                description: \"Please select a resume before uploading.\"\n            });\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"resume\", selectedResume);\n        try {\n            setIsUploading(true);\n            const postResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.post(\"/register/upload-image\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n            const { Location } = postResponse.data.data;\n            if (!Location) throw new Error(\"Failed to upload the resume.\");\n            const putResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.put(\"/freelancer\", {\n                resume: Location\n            });\n            if (putResponse.status === 200) {\n                setExistingResumeUrl(Location);\n                setUploadedFileName(selectedResume.name);\n                setSelectedResume(null);\n                setResumePreviewURL(Location);\n                // Notify parent component if callback provided\n                if (onResumeUpdate) {\n                    onResumeUpdate();\n                }\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    title: \"Success\",\n                    description: \"Resume uploaded successfully!\"\n                });\n            } else {\n                throw new Error(\"Failed to update resume.\");\n            }\n        } catch (error) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Something went wrong. Please try again.\"\n            });\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchResume = async ()=>{\n            try {\n                var _response_data_data, _response_data, _response_data1;\n                // Use the same endpoint as the profile form for consistency\n                const endpoint = userId ? \"/freelancer/\".concat(userId) : \"/freelancer\";\n                const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_4__.axiosInstance.get(endpoint);\n                // Check if response has the expected structure\n                const resumeUrl = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.resume) || ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.resume);\n                if (resumeUrl && resumeUrl.trim() !== \"\") {\n                    setExistingResumeUrl(resumeUrl);\n                    setUploadedFileName(extractFileNameFromUrl(resumeUrl));\n                    setResumePreviewURL(resumeUrl); // Set the preview URL\n                } else {\n                    // Clear states if no resume\n                    setExistingResumeUrl(null);\n                    setUploadedFileName(null);\n                    setResumePreviewURL(null);\n                }\n            } catch (error) {\n                console.error(\"Error fetching resume:\", error);\n            }\n        };\n        fetchResume();\n    }, [\n        userId\n    ]);\n    const handleCancelClick = (e)=>{\n        e.stopPropagation();\n        setSelectedResume(null);\n        setResumePreviewURL(null);\n        // If there's an existing resume, don't clear the uploaded filename\n        if (!existingResumeUrl) {\n            setUploadedFileName(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"upload-form max-w-md mx-auto rounded shadow-md p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 flex flex-col items-center\",\n            children: [\n                existingResumeUrl && !selectedResume ? // Show existing resume with options to change or remove\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full border border-gray-300 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"text-green-600 w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-white\",\n                                                children: \"Resume Uploaded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: truncateFileName(uploadedFileName || \"resume.pdf\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>window.open(existingResumeUrl, \"_blank\"),\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"View\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    onClick: handleRemoveResume,\n                                    disabled: isRemoving,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isRemoving ? \"Removing...\" : \"Remove\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, undefined) : // Show upload area\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center border-dashed border-2 border-gray-400 rounded-lg p-6 w-full cursor-pointer\",\n                    onClick: ()=>{\n                        var _fileInputRef_current;\n                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                    },\n                    children: selectedResume ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex flex-col items-center gap-4 text-gray-700 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"truncate\",\n                                        children: truncateFileName(selectedResume.name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-red-600 text-white rounded-full p-1 hover:bg-red-700\",\n                                        onClick: (e)=>handleCancelClick(e),\n                                        \"aria-label\": \"Remove file\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 17\n                            }, undefined),\n                            resumePreviewURL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: resumePreviewURL,\n                                title: \"Resume Preview\",\n                                className: \"w-full h-40 border rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 19\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 p-2 bg-gray-100 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"text-gray-500 w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: truncateFileName(selectedResume.name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 15\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_FileText_Trash2_UploadCloud_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"text-gray-500 w-12 h-12 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 text-center\",\n                                children: existingResumeUrl ? \"Select a new resume to replace the current one\" : \"Drag and drop your resume here or click to upload\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600 text-xs md:text-sm\",\n                                    children: \"Supported formats: PDF, DOCX.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                accept: allowedResumeFormats.join(\",\"),\n                                onChange: handleResumeChange,\n                                className: \"hidden\",\n                                ref: fileInputRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined),\n                selectedResume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: handleUploadClick,\n                    className: \"w-full\",\n                    disabled: isUploading,\n                    children: isUploading ? \"Uploading...\" : \"Upload Resume\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined),\n                uploadedFileName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-gray-600\",\n                    children: [\n                        \"Uploaded:\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: truncateFileName(uploadedFileName || \"\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\fileUpload\\\\resume.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeUpload, \"yfJ4IaG2TS7UGOeDeIGVofeZZg0=\");\n_c = ResumeUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ResumeUpload);\nvar _c;\n$RefreshReg$(_c, \"ResumeUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fileUpload/resume.tsx\n"));

/***/ })

});