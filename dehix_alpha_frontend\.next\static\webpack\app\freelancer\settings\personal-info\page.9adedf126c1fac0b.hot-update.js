"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/freelancer/settings/personal-info/page",{

/***/ "(app-pages-browser)/./src/components/form/profileForm.tsx":
/*!*********************************************!*\
  !*** ./src/components/form/profileForm.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileForm: function() { return /* binding */ ProfileForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _fileUpload_profilePicture__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../fileUpload/profilePicture */ \"(app-pages-browser)/./src/components/fileUpload/profilePicture.tsx\");\n/* harmony import */ var _fileUpload_resume__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../fileUpload/resume */ \"(app-pages-browser)/./src/components/fileUpload/resume.tsx\");\n/* harmony import */ var _CoverLetterTextarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CoverLetterTextarea */ \"(app-pages-browser)/./src/components/form/CoverLetterTextarea.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _utils_enum__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/enum */ \"(app-pages-browser)/./src/utils/enum.ts\");\n/* harmony import */ var _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/freelancer/enum */ \"(app-pages-browser)/./src/utils/freelancer/enum.ts\");\n/* harmony import */ var _utils_skillUtils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/skillUtils */ \"(app-pages-browser)/./src/utils/skillUtils.ts\");\n/* harmony import */ var _utils_DomainUtils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/DomainUtils */ \"(app-pages-browser)/./src/utils/DomainUtils.ts\");\n/* harmony import */ var _utils_ProjectDomainUtils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/ProjectDomainUtils */ \"(app-pages-browser)/./src/utils/ProjectDomainUtils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst profileFormSchema = zod__WEBPACK_IMPORTED_MODULE_21__.z.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().min(2, {\n        message: \"First Name must be at least 2 characters.\"\n    }),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().min(2, {\n        message: \"Last Name must be at least 2 characters.\"\n    }),\n    username: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().min(2, {\n        message: \"Username must be at least 2 characters.\"\n    }).max(30, {\n        message: \"Username must not be longer than 30 characters.\"\n    }),\n    email: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().email(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().min(10, {\n        message: \"Phone number must be at least 10 digits.\"\n    }),\n    role: zod__WEBPACK_IMPORTED_MODULE_21__.z.string(),\n    personalWebsite: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().url().optional(),\n    coverLetter: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().optional().refine((val)=>{\n        // If no value provided, it's valid (optional field)\n        if (!val || val.trim() === \"\") return true;\n        // If value is provided, check minimum requirements\n        const wordCount = val.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n        return val.length >= 500 && wordCount >= 500;\n    }, {\n        message: \"Cover letter must contain at least 500 words and 500 characters when provided.\"\n    }),\n    description: zod__WEBPACK_IMPORTED_MODULE_21__.z.string().max(500, {\n        message: \"Description cannot exceed 500 characters.\"\n    })\n});\nfunction ProfileForm(param) {\n    let { user_id } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currSkills, setCurrSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tmpSkill, setTmpSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currDomains, setCurrDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tmpDomain, setTmpDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectDomains, setProjectDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currProjectDomains, setCurrProjectDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tmpProjectDomains, setTmpProjectDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeRefreshTrigger, setResumeRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [, setLastAddedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        skills: [],\n        projectsDomains: [],\n        domains: []\n    });\n    const [customSkill, setCustomSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        description: \"\"\n    });\n    const [customDomain, setCustomDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        description: \"\"\n    });\n    const [customProjectDomain, setCustomProjectDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        description: \"\"\n    });\n    const [dialogType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_22__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(profileFormSchema),\n        defaultValues: {\n            firstName: \"\",\n            lastName: \"\",\n            username: \"\",\n            email: \"\",\n            phone: \"\",\n            role: \"\",\n            personalWebsite: \"\",\n            coverLetter: \"\",\n            description: \"\"\n        },\n        mode: \"all\"\n    });\n    const handleAddSkill = ()=>{\n        (0,_utils_skillUtils__WEBPACK_IMPORTED_MODULE_18__.addSkill)(tmpSkill, skills, setSkills);\n        if (tmpSkill && !currSkills.some((skill)=>skill.name === tmpSkill)) {\n            setCurrSkills([\n                ...currSkills,\n                {\n                    name: tmpSkill,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.PENDING,\n                    interviewInfo: \"\",\n                    interviewerRating: 0\n                }\n            ]);\n            setLastAddedItems((prev)=>({\n                    ...prev,\n                    skills: [\n                        ...prev.skills,\n                        {\n                            name: tmpSkill\n                        }\n                    ]\n                }));\n            setTmpSkill(\"\");\n        }\n    };\n    const handleAddCustomSkill = async ()=>{\n        if (!customSkill.label.trim()) {\n            console.warn(\"Field is required.\");\n            return;\n        }\n        const customSkillData = {\n            label: customSkill.label,\n            interviewInfo: customSkill.description,\n            createdBy: _utils_enum__WEBPACK_IMPORTED_MODULE_16__.Type.FREELANCER,\n            createdById: user_id,\n            status: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.ACTIVE\n        };\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.post(\"/skills\", customSkillData);\n            const updatedSkills = [\n                ...skills,\n                {\n                    label: customSkill.label\n                }\n            ];\n            setDomains(updatedSkills);\n            setCurrSkills([\n                ...currSkills,\n                {\n                    name: customSkill.label,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: \"PENDING\",\n                    interviewInfo: customSkill.description,\n                    interviewerRating: 0\n                }\n            ]);\n            setCustomSkill({\n                label: \"\",\n                description: \"\"\n            });\n            setIsDialogOpen(false);\n        } catch (error) {\n            var _error_response;\n            console.error(\"Failed to add skill:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to add skill. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddCustomDomain = async ()=>{\n        if (!customDomain.label.trim()) {\n            console.warn(\"Field is required.\");\n            return;\n        }\n        const customDomainData = {\n            label: customDomain.label,\n            interviewInfo: customSkill.description,\n            createdBy: _utils_enum__WEBPACK_IMPORTED_MODULE_16__.Type.FREELANCER,\n            createdById: user_id,\n            status: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.ACTIVE\n        };\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.post(\"/domain\", customDomainData);\n            const updatedDomains = [\n                ...domains,\n                {\n                    label: customDomain.label\n                }\n            ];\n            setDomains(updatedDomains);\n            setCurrDomains([\n                ...currDomains,\n                {\n                    name: customDomain.label,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: \"PENDING\",\n                    interviewInfo: customDomain.description,\n                    interviewerRating: 0\n                }\n            ]);\n            setCustomDomain({\n                label: \"\",\n                description: \"\"\n            });\n            setIsDialogOpen(false);\n        } catch (error) {\n            var _error_response;\n            console.error(\"Failed to add domain:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to add domain. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddCustomProjectDomain = async ()=>{\n        if (!customProjectDomain.label.trim()) {\n            console.warn(\"Field is required.\");\n            return;\n        }\n        const customProjectDomainData = {\n            label: customProjectDomain.label,\n            createdBy: _utils_enum__WEBPACK_IMPORTED_MODULE_16__.Type.FREELANCER,\n            createdById: user_id,\n            status: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.ACTIVE\n        };\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.post(\"/projectdomain\", customProjectDomainData);\n            const updatedProjectDomains = [\n                ...projectDomains,\n                {\n                    label: customProjectDomain.label\n                }\n            ];\n            setProjectDomains(updatedProjectDomains);\n            setCurrProjectDomains([\n                ...currProjectDomains,\n                {\n                    name: customProjectDomain.label,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: \"PENDING\",\n                    interviewInfo: customProjectDomain.description,\n                    interviewerRating: 0\n                }\n            ]);\n            setCustomProjectDomain({\n                label: \"\",\n                description: \"\"\n            });\n            setIsDialogOpen(false);\n        } catch (error) {\n            var _error_response;\n            console.error(\"Failed to add project domain:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to add project domain. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddDomain = ()=>{\n        (0,_utils_DomainUtils__WEBPACK_IMPORTED_MODULE_19__.addDomain)(tmpDomain, domains, setDomains);\n        if (tmpDomain && !currDomains.some((domain)=>domain.name === tmpDomain)) {\n            setCurrDomains([\n                ...currDomains,\n                {\n                    name: tmpDomain,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.PENDING,\n                    interviewInfo: \"\",\n                    interviewerRating: 0\n                }\n            ]);\n            setLastAddedItems((prev)=>({\n                    ...prev,\n                    domains: [\n                        ...prev.domains,\n                        {\n                            name: tmpDomain\n                        }\n                    ]\n                }));\n            setTmpDomain(\"\");\n        }\n    };\n    const handleAddprojectDomain = ()=>{\n        (0,_utils_ProjectDomainUtils__WEBPACK_IMPORTED_MODULE_20__.addProjectDomain)(tmpProjectDomains, projectDomains, setProjectDomains);\n        if (tmpProjectDomains && !currProjectDomains.some((projectDomains)=>projectDomains.name === projectDomains)) {\n            setCurrProjectDomains([\n                ...currProjectDomains,\n                {\n                    name: tmpProjectDomains,\n                    level: \"\",\n                    experience: \"\",\n                    interviewStatus: _utils_freelancer_enum__WEBPACK_IMPORTED_MODULE_17__.StatusEnum.PENDING,\n                    interviewInfo: \"\",\n                    interviewerRating: 0\n                }\n            ]);\n            setLastAddedItems((prev)=>({\n                    ...prev,\n                    projectsDomains: [\n                        ...prev.projectsDomains,\n                        {\n                            name: tmpProjectDomains\n                        }\n                    ]\n                }));\n            setTmpProjectDomains(\"\");\n        }\n    };\n    const handleDeleteSkill = (skillToDelete)=>{\n        setCurrSkills(currSkills.filter((skill)=>skill.name !== skillToDelete));\n    };\n    const handleDeleteDomain = (domainToDelete)=>{\n        setCurrDomains(currDomains.filter((domain)=>domain.name !== domainToDelete));\n    };\n    const handleDeleteProjDomain = (projectDomainToDelete)=>{\n        setCurrProjectDomains(currProjectDomains.filter((projectDomain)=>projectDomain.name !== projectDomainToDelete));\n    };\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            try {\n                const userResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.get(\"/freelancer/\".concat(user_id));\n                setUser(userResponse.data.data);\n                const skillsResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.get(\"/skills\");\n                const domainsResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.get(\"/domain\");\n                const projectDomainResponse = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.get(\"/projectdomain\");\n                // Set options for dropdowns\n                setSkills(skillsResponse.data.data);\n                console.log(\"\", skills);\n                setDomains(domainsResponse.data.data);\n                setProjectDomains(projectDomainResponse.data.data);\n                setCurrSkills(userResponse.data.data.skills);\n                setCurrDomains(userResponse.data.data.domain);\n                setCurrProjectDomains(userResponse.data.data.projectDomain);\n                // Ensure cover letter is treated as text, not URL\n                const coverLetterValue = userResponse.data.data.coverLetter;\n                const cleanCoverLetter = coverLetterValue && typeof coverLetterValue === \"string\" && !coverLetterValue.startsWith(\"http\") ? coverLetterValue : \"\";\n                form.reset({\n                    firstName: userResponse.data.data.firstName || \"\",\n                    lastName: userResponse.data.data.lastName || \"\",\n                    username: userResponse.data.data.userName || \"\",\n                    email: userResponse.data.data.email || \"\",\n                    phone: userResponse.data.data.phone || \"\",\n                    role: userResponse.data.data.role || \"\",\n                    personalWebsite: userResponse.data.data.personalWebsite || \"\",\n                    coverLetter: cleanCoverLetter,\n                    description: userResponse.data.data.description || \"\"\n                });\n            } catch (error) {\n                console.error(\"API Error:\", error);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"Something went wrong.Please try again.\"\n                });\n            }\n        };\n        fetchData();\n    }, [\n        user_id,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        form.reset({\n            firstName: (user === null || user === void 0 ? void 0 : user.firstName) || \"\",\n            lastName: (user === null || user === void 0 ? void 0 : user.lastName) || \"\",\n            username: (user === null || user === void 0 ? void 0 : user.userName) || \"\",\n            email: (user === null || user === void 0 ? void 0 : user.email) || \"\",\n            phone: (user === null || user === void 0 ? void 0 : user.phone) || \"\",\n            role: (user === null || user === void 0 ? void 0 : user.role) || \"\",\n            personalWebsite: (user === null || user === void 0 ? void 0 : user.personalWebsite) || \"\",\n            coverLetter: (user === null || user === void 0 ? void 0 : user.coverLetter) || \"\",\n            description: (user === null || user === void 0 ? void 0 : user.description) || \"\"\n        });\n    }, [\n        user,\n        form\n    ]);\n    async function onSubmit(data) {\n        setLoading(true);\n        try {\n            const { ...restData } = data;\n            const updatedSkills = currSkills.map((skill)=>({\n                    ...skill,\n                    interviewInfo: skill.interviewInfo || \"\",\n                    interviewerRating: skill.interviewerRating || 0,\n                    interviewStatus: skill.interviewStatus || \"PENDING\"\n                }));\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_8__.axiosInstance.put(\"/freelancer\", {\n                ...restData,\n                coverLetter: data.coverLetter,\n                skills: updatedSkills,\n                domain: currDomains,\n                projectDomain: currProjectDomains,\n                description: data.description\n            });\n            setUser({\n                ...user,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                userName: data.username,\n                email: data.email,\n                phone: data.phone,\n                role: data.role,\n                personalWebsite: data.personalWebsite,\n                coverLetter: data.coverLetter,\n                skills: updatedSkills,\n                domain: currDomains,\n                projectDomains: currProjectDomains\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Profile Updated\",\n                description: \"Your profile has been successfully updated.\"\n            });\n            // Trigger resume component refresh with a small delay to ensure backend processing\n            setTimeout(()=>{\n                setResumeRefreshTrigger((prev)=>prev + 1);\n            }, 500);\n        } catch (error) {\n            console.error(\"API Error:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to update profile. Please try again later.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"p-10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.Form, {\n            ...form,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fileUpload_profilePicture__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    profile: user.profilePic,\n                    entityType: _utils_enum__WEBPACK_IMPORTED_MODULE_16__.Type.FREELANCER\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: form.handleSubmit(onSubmit),\n                    className: \"grid gap-10 grid-cols-1 sm:grid-cols-2 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"firstName\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"First Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"Enter your first name\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"lastName\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Last Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"Enter your last name\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"username\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"Enter your username\",\n                                                ...field,\n                                                readOnly: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormDescription, {\n                                            children: \"Non editable field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"email\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"Enter your email\",\n                                                ...field,\n                                                readOnly: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormDescription, {\n                                            children: \"Non editable field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"description\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    className: \"sm:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                placeholder: \"Enter description\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"phone\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Phone Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"+91\",\n                                                ...field,\n                                                readOnly: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormDescription, {\n                                            children: \"Non editable field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                            control: form.control,\n                            name: \"personalWebsite\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                            children: \"Personal Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                placeholder: \"Enter your LinkedIn URL\",\n                                                type: \"url\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormDescription, {\n                                            children: \"Enter your Personal Website URL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {\n                            className: \"col-span-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-10 grid-cols-1 sm:grid-cols-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-[350px] max-w-[500px] mt-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                                    children: \"Skills\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            onValueChange: (value)=>{\n                                                                setTmpSkill(value);\n                                                                setSearchQuery(\"\"); // Reset search query when a value is selected\n                                                            },\n                                                            value: tmpSkill || \"\",\n                                                            onOpenChange: (open)=>{\n                                                                if (!open) setSearchQuery(\"\");\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                        placeholder: tmpSkill ? tmpSkill : \"Select skill\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"text\",\n                                                                                    value: searchQuery,\n                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                    placeholder: \"Search skills\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        skills.filter((skill)=>skill.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currSkills.some((s)=>s.name === skill.label)).map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                value: skill.label,\n                                                                                children: skill.label\n                                                                            }, index, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 29\n                                                                            }, this)),\n                                                                        skills.filter((skill)=>skill.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currSkills.some((s)=>s.name === skill.label)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                            children: \"No matching skills\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                            variant: \"outline\",\n                                                            type: \"button\",\n                                                            size: \"icon\",\n                                                            className: \"ml-2\",\n                                                            disabled: !tmpSkill,\n                                                            onClick: ()=>{\n                                                                handleAddSkill();\n                                                                setTmpSkill(\"\");\n                                                                setSearchQuery(\"\"); // Reset search query\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                    children: currSkills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                            children: [\n                                                                skill.name,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>handleDeleteSkill(skill.name),\n                                                                    className: \"ml-2 text-red-500 hover:text-red-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-[350px] max-w-[500px] mt-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                                    children: \"Domains\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            onValueChange: (value)=>{\n                                                                setTmpDomain(value);\n                                                                setSearchQuery(\"\"); // Reset search query when a value is selected\n                                                            },\n                                                            value: tmpDomain || \"\",\n                                                            onOpenChange: (open)=>{\n                                                                if (!open) setSearchQuery(\"\");\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                        placeholder: tmpDomain ? tmpDomain : \"Select domain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"text\",\n                                                                                    value: searchQuery,\n                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                    placeholder: \"Search domains\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 755,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 763,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        domains.filter((domain)=>domain.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currDomains.some((s)=>s.name === domain.label)).map((domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                value: domain.label,\n                                                                                children: domain.label\n                                                                            }, index, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 29\n                                                                            }, this)),\n                                                                        domains.filter((Domain)=>Domain.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currDomains.some((s)=>s.name === domains.name)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                            children: \"No matching domains\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                            variant: \"outline\",\n                                                            type: \"button\",\n                                                            size: \"icon\",\n                                                            className: \"ml-2\",\n                                                            disabled: !tmpDomain,\n                                                            onClick: ()=>{\n                                                                handleAddDomain();\n                                                                setTmpDomain(\"\");\n                                                                setSearchQuery(\"\"); // Reset search query\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                    children: currDomains.map((Domain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                            children: [\n                                                                Domain.name,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>handleDeleteDomain(Domain.name),\n                                                                    className: \"ml-2 text-red-500 hover:text-red-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-[350px] max-w-[500px] mt-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                                    children: \"Project Domains\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 839,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            onValueChange: (value)=>{\n                                                                setTmpProjectDomains(value);\n                                                                setSearchQuery(\"\"); // Reset search query when a value is selected\n                                                            },\n                                                            value: tmpProjectDomains || \"\",\n                                                            onOpenChange: (open)=>{\n                                                                if (!open) setSearchQuery(\"\");\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectValue, {\n                                                                        placeholder: tmpProjectDomains ? tmpProjectDomains : \"Select project domain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"text\",\n                                                                                    value: searchQuery,\n                                                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                                                    className: \"w-full p-2 border border-gray-300 rounded-lg text-sm\",\n                                                                                    placeholder: \"Search project domains\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white text-xl transition-colors mr-2\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                    lineNumber: 871,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 862,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        projectDomains.filter((projectDomain)=>projectDomain.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currProjectDomains.some((s)=>s.name === projectDomain.label)).map((projectDomain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_15__.SelectItem, {\n                                                                                value: projectDomain.label,\n                                                                                children: projectDomain.label\n                                                                            }, index, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                                lineNumber: 891,\n                                                                                columnNumber: 29\n                                                                            }, this)),\n                                                                        projectDomains.filter((projectDomain)=>projectDomain.label.toLowerCase().includes(searchQuery.toLowerCase()) && !currProjectDomains.some((s)=>s.name === projectDomains.name)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 text-gray-500 italic text-center\",\n                                                                            children: \"No matching domains\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                            variant: \"outline\",\n                                                            type: \"button\",\n                                                            size: \"icon\",\n                                                            className: \"ml-2\",\n                                                            disabled: !tmpProjectDomains,\n                                                            onClick: ()=>{\n                                                                handleAddprojectDomain();\n                                                                setTmpProjectDomains(\"\");\n                                                                setSearchQuery(\"\"); // Reset search query\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 923,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 mt-5\",\n                                                    children: currProjectDomains.map((projectDomain, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                            className: \"uppercase text-xs font-normal bg-gray-300 flex items-center px-2 py-1\",\n                                                            children: [\n                                                                projectDomain.name,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>handleDeleteProjDomain(projectDomain.name),\n                                                                    className: \"ml-2 text-red-500 hover:text-red-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 941,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {\n                            className: \"col-span-2 mt-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-10 grid-cols-1 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                                    className: \"ml-2\",\n                                                    children: \"Upload Resume\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fileUpload_resume__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        refreshTrigger: resumeRefreshTrigger,\n                                                        userId: user_id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormField, {\n                                            control: form.control,\n                                            name: \"coverLetter\",\n                                            render: (param)=>{\n                                                let { field, fieldState } = param;\n                                                var _fieldState_error;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormItem, {\n                                                    className: \"flex flex-col items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_10__.FormLabel, {\n                                                            className: \"ml-2\",\n                                                            children: \"Cover Letter (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 968,\n                                                            columnNumber: 21\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CoverLetterTextarea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                value: field.value || \"\",\n                                                                onChange: field.onChange,\n                                                                error: (_fieldState_error = fieldState.error) === null || _fieldState_error === void 0 ? void 0 : _fieldState_error.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 21\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                    lineNumber: 967,\n                                                    columnNumber: 19\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_13__.Separator, {\n                                    className: \"sm:col-span-2 mt-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                type: \"submit\",\n                                className: \"sm:col-span-2 w-full\",\n                                disabled: loading,\n                                children: loading ? \"Loading...\" : \"Update Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                lineNumber: 985,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 984,\n                            columnNumber: 11\n                        }, this),\n                        isDialogOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                            open: isDialogOpen,\n                            onOpenChange: (isOpen)=>setIsDialogOpen(isOpen),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_25__.DialogOverlay, {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_25__.DialogContent, {\n                                    className: \"fixed inset-0 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-black rounded-md shadow-xl p-6 w-[90%] max-w-md\",\n                                        children: [\n                                            dialogType === \"skill\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold text-white mb-4\",\n                                                        children: \"Add New Skill\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: (e)=>{\n                                                            e.preventDefault();\n                                                            handleAddCustomSkill(); // Add custom skill logic\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"skillLabel\",\n                                                                        className: \"block text-sm font-medium text-white mb-1\",\n                                                                        children: \"Skill Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: customSkill.label,\n                                                                        onChange: (e)=>setCustomSkill({\n                                                                                ...customSkill,\n                                                                                label: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter skill label\",\n                                                                        className: \"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>setIsDialogOpen(false),\n                                                                        className: \"mt-3\",\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1035,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        className: \"mt-3\",\n                                                                        onClick: ()=>{\n                                                                            handleAddCustomSkill();\n                                                                            setCustomSkill({\n                                                                                label: \"\",\n                                                                                description: \"\"\n                                                                            });\n                                                                        },\n                                                                        children: \"Add Skill\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1043,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true),\n                                            dialogType === \"domain\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold text-white mb-4\",\n                                                        children: \"Add New Domain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1059,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: (e)=>{\n                                                            e.preventDefault();\n                                                            handleAddCustomDomain(); // Add custom domain logic\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"domainLabel\",\n                                                                        className: \"block text-sm font-medium text-white mb-1\",\n                                                                        children: \"Domain Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1069,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: customDomain.label,\n                                                                        onChange: (e)=>setCustomDomain({\n                                                                                ...customDomain,\n                                                                                label: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter Domain label\",\n                                                                        className: \"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1075,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>setIsDialogOpen(false),\n                                                                        className: \"mt-3\",\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1090,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        className: \"mt-3\",\n                                                                        onClick: ()=>{\n                                                                            handleAddCustomDomain();\n                                                                            setCustomDomain({\n                                                                                label: \"\",\n                                                                                description: \"\"\n                                                                            });\n                                                                        },\n                                                                        children: \"Add Domain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true),\n                                            dialogType === \"projectDomain\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-semibold text-white mb-4\",\n                                                        children: \"Add New Project Domain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: (e)=>{\n                                                            e.preventDefault();\n                                                            handleAddCustomProjectDomain(); // Add custom project domain logic\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"projectDomainLabel\",\n                                                                        className: \"block text-sm font-medium text-white mb-1\",\n                                                                        children: \"Project Domain Label\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: customProjectDomain.label,\n                                                                        onChange: (e)=>setCustomProjectDomain({\n                                                                                ...customProjectDomain,\n                                                                                label: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter Project Domain label\",\n                                                                        className: \"w-full px-3 py-2 rounded-md text-white bg-black placeholder-gray-400 border border-white\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1130,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1123,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>setIsDialogOpen(false),\n                                                                        className: \"mt-3\",\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1145,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                        type: \"button\",\n                                                                        className: \"mt-3\",\n                                                                        onClick: ()=>{\n                                                                            handleAddCustomProjectDomain();\n                                                                            setCustomProjectDomain({\n                                                                                label: \"\",\n                                                                                description: \"\"\n                                                                            });\n                                                                        },\n                                                                        children: \"Add Project Domain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                                lineNumber: 1144,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                            lineNumber: 995,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n            lineNumber: 511,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\profileForm.tsx\",\n        lineNumber: 510,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfileForm, \"0Yy51m1mhuazh1uTBnMrVRYxLRE=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_22__.useForm\n    ];\n});\n_c = ProfileForm;\nvar _c;\n$RefreshReg$(_c, \"ProfileForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/form/profileForm.tsx\n"));

/***/ })

});